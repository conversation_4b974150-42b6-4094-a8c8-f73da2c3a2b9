'use client';

import React from 'react';
import ProviderSelector from '@/components/ProviderSelector';
import APIKeyInput from '@/components/APIKeyInput';
import ModelSelector from '@/components/ModelSelector';
import ImageGenerator from '@/components/ImageGenerator';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      {/* 頁面標題 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI 圖片處理應用程式
            </h1>
            <p className="mt-2 text-lg text-gray-600">
              使用 Gemini 和 OpenRouter AI 進行圖片生成與分析
            </p>
          </div>
        </div>
      </div>

      {/* 主要內容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左側設定區域 */}
          <div className="lg:col-span-1 space-y-6">
            <ProviderSelector />
            <APIKeyInput />
            <ModelSelector />
          </div>

          {/* 右側圖片生成區域 */}
          <div className="lg:col-span-2">
            <ImageGenerator />
          </div>
        </div>
      </div>

      {/* 頁腳 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p className="text-sm">
              © 2025 Gemini 圖片生成 -
              <a
                href="https://ai.google.dev/"
                target="_blank"
                rel="noopener noreferrer"
                className="ml-1 text-blue-500 hover:text-blue-600 hover:underline"
              >
                Powered by Google Gemini AI
              </a>
            </p>
            <div className="mt-4 flex justify-center space-x-6">
              <a
                href="https://ai.google.dev/gemini-api/docs"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <span className="sr-only">API 文件</span>
                <i className="pi pi-book text-lg"></i>
              </a>
              <a
                href="https://aistudio.google.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <span className="sr-only">Google AI Studio</span>
                <i className="pi pi-external-link text-lg"></i>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
