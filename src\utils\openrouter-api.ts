// OpenRouter API 整合模組
// 基於 OpenRouter API 文件和範例實作

const OPENROUTER_API_BASE_URL = 'https://openrouter.ai/api/v1';

// OpenRouter API 錯誤類型
export class OpenRouterAPIError extends Error {
  constructor(
    message: string,
    public code?: number,
    public status?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'OpenRouterAPIError';
  }

  getLocalizedMessage(): string {
    switch (this.code) {
      case 401:
        return 'API Key 無效或已過期，請檢查您的 OpenRouter API Key';
      case 403:
        return '權限不足，請檢查您的 API Key 權限設定';
      case 429:
        return '請求過於頻繁，請稍後再試';
      case 500:
        return 'OpenRouter 服務暫時不可用，請稍後再試';
      default:
        return this.message || '未知錯誤';
    }
  }
}

// OpenRouter API 請求介面
export interface OpenRouterRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: Array<{
      type: 'text' | 'image_url';
      text?: string;
      image_url?: {
        url: string;
      };
    }>;
  }>;
  max_tokens?: number;
  temperature?: number;
}

// OpenRouter API 回應介面
export interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface OpenRouterErrorResponse {
  error: {
    message: string;
    type: string;
    code?: number;
  };
}

/**
 * 處理 OpenRouter API 回應
 */
async function handleOpenRouterResponse(response: Response): Promise<OpenRouterResponse> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

    try {
      const errorData: OpenRouterErrorResponse = await response.json();
      errorMessage = errorData.error?.message || errorMessage;

      throw new OpenRouterAPIError(
        errorMessage,
        response.status,
        response.statusText,
        errorData
      );
    } catch (parseError) {
      if (parseError instanceof OpenRouterAPIError) {
        throw parseError;
      }
      throw new OpenRouterAPIError(errorMessage, response.status, response.statusText);
    }
  }

  try {
    const data: OpenRouterResponse = await response.json();
    return data;
  } catch (parseError) {
    throw new OpenRouterAPIError('無法解析 API 回應', 0, 'PARSE_ERROR');
  }
}

/**
 * 將檔案轉換為 base64 格式
 */
async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      resolve(result);
    };
    reader.onerror = () => reject(new Error('檔案讀取失敗'));
    reader.readAsDataURL(file);
  });
}

/**
 * 驗證 OpenRouter API Key 是否有效
 */
export async function validateOpenRouterApiKey(apiKey: string): Promise<{ isValid: boolean; error?: string }> {
  try {
    // 使用一個簡單的模型列表請求來驗證 API Key
    const response = await fetch(`${OPENROUTER_API_BASE_URL}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      return { isValid: true };
    }

    // 處理錯誤回應
    try {
      await handleOpenRouterResponse(response);
      return { isValid: false };
    } catch (apiError) {
      if (apiError instanceof OpenRouterAPIError) {
        return {
          isValid: false,
          error: apiError.getLocalizedMessage()
        };
      }
      return {
        isValid: false,
        error: apiError instanceof Error ? apiError.message : 'API Key 驗證失敗'
      };
    }
  } catch (error) {
    console.error('OpenRouter API Key 驗證失敗:', error);
    return {
      isValid: false,
      error: '網路連線錯誤，請檢查網路連線後再試'
    };
  }
}

/**
 * 使用 OpenRouter API 分析圖片
 */
export async function analyzeImageWithOpenRouter(
  apiKey: string,
  prompt: string,
  model: string,
  image: File
): Promise<string> {
  try {
    // 將圖片轉換為 base64
    const base64Image = await fileToBase64(image);

    const request: OpenRouterRequest = {
      model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt || "請描述這張圖片的內容。",
            },
            {
              type: 'image_url',
              image_url: {
                url: base64Image,
              },
            },
          ],
        },
      ],
      max_tokens: 1000,
      temperature: 0.7,
    };

    const response = await fetch(`${OPENROUTER_API_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    const data = await handleOpenRouterResponse(response);

    if (data.choices && data.choices.length > 0) {
      return data.choices[0].message.content;
    }

    throw new OpenRouterAPIError('API 回應中沒有找到分析結果');
  } catch (error) {
    console.error('OpenRouter 圖片分析失敗:', error);

    if (error instanceof OpenRouterAPIError) {
      throw error;
    }

    throw new OpenRouterAPIError(
      error instanceof Error ? error.message : '圖片分析失敗'
    );
  }
}

/**
 * 下載分析結果為文字檔案
 */
export function downloadAnalysisResult(result: string, filename: string = 'analysis-result.txt'): void {
  try {
    const blob = new Blob([result], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下載分析結果失敗:', error);
    throw new Error('下載分析結果失敗');
  }
}
