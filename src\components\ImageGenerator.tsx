'use client';

import React, { useRef } from 'react';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { Image } from 'primereact/image';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Toast } from 'primereact/toast';
import { Dropdown } from 'primereact/dropdown';
import ImageUpload from './ImageUpload';
import ErrorDisplay from './ErrorDisplay';
import {
  useApiKey,
  useModel,
  useImageGeneration,
  usePrompts,
  useReferenceImage,
  useError,
  ALL_MODELS
} from '@/hooks/useGemini';
import { DEFAULT_PROMPTS } from '@/data/prompts';
import { processImage, combinePrompts, downloadResult } from '@/utils/ai-api';

interface ImageGeneratorProps {
  className?: string;
}

export default function ImageGenerator({ className = '' }: ImageGeneratorProps) {
  const { api<PERSON><PERSON>, isApi<PERSON><PERSON><PERSON>alid, selectedProvider } = useApiKey();
  const { selectedModel } = useModel();
  const {
    isGenerating,
    generatedImage,
    analysisResult,
    setIsGenerating,
    setGeneratedImage,
    setAnalysisResult
  } = useImageGeneration();
  const { prompt, refinementPrompt, setPrompt, setRefinementPrompt } = usePrompts();
  const { referenceImage, setReferenceImage, setReferenceImagePreview } = useReferenceImage();
  const { error, setError } = useError();
  const toast = useRef<Toast>(null);

  const selectedModelInfo = ALL_MODELS.find(model => model.value === selectedModel);
  const isImageModel = selectedModelInfo?.type === 'image';
  const isAnalysisProvider = selectedProvider === 'openrouter';

  // 準備提示詞選項
  const promptOptions = [
    { label: '自訂提示詞', value: null, icon: 'pi-pencil' },
    ...DEFAULT_PROMPTS.map(prompt => ({
      label: prompt.name,
      value: prompt.id,
      description: prompt.description,
      prompt: prompt.prompt,
      icon: prompt.icon
    }))
  ];

  const handlePromptSelection = (e: { value: string | null }) => {
    if (e.value) {
      const selectedPrompt = DEFAULT_PROMPTS.find(p => p.id === e.value);
      if (selectedPrompt) {
        setPrompt(selectedPrompt.prompt);
      }
    } else {
      setPrompt('');
    }
  };



  const handleProcessImage = async () => {
    if (!isApiKeyValid) {
      setError('請先設定並驗證 API Key');
      return;
    }

    if (!prompt.trim()) {
      setError('請輸入提示詞');
      return;
    }

    if (!isImageModel) {
      setError('請選擇圖片處理模型');
      return;
    }

    // OpenRouter 需要參考圖片
    if (isAnalysisProvider && !referenceImage) {
      setError('OpenRouter 需要上傳圖片才能進行分析');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setGeneratedImage(null);
    setAnalysisResult(null);

    try {
      let finalPrompt = prompt.trim();

      // 如果有微調提示詞，先進行組合
      if (refinementPrompt.trim()) {
        toast.current?.show({
          severity: 'info',
          summary: '正在組合提示詞',
          detail: '使用 AI 模型組合基礎提示詞和微調提示詞...',
          life: 3000
        });

        try {
          finalPrompt = await combinePrompts(
            selectedProvider,
            apiKey,
            prompt.trim(),
            refinementPrompt.trim()
          );

          toast.current?.show({
            severity: 'success',
            summary: '提示詞組合完成',
            detail: `開始${isAnalysisProvider ? '分析圖片' : '生成圖片'}...`,
            life: 2000
          });
        } catch (combineError) {
          console.warn('提示詞組合失敗，使用原始提示詞:', combineError);
          toast.current?.show({
            severity: 'warn',
            summary: '提示詞組合失敗',
            detail: `將使用原始提示詞進行${isAnalysisProvider ? '圖片分析' : '圖片生成'}`,
            life: 3000
          });
        }
      }

      // 處理圖片（生成或分析）
      const result = await processImage(
        selectedProvider,
        apiKey,
        finalPrompt,
        selectedModel,
        referenceImage || undefined
      );

      if (result.type === 'generation') {
        setGeneratedImage(result.result);
        toast.current?.show({
          severity: 'success',
          summary: '圖片生成成功',
          detail: '您的圖片已經生成完成！',
          life: 3000
        });
      } else if (result.type === 'analysis') {
        setAnalysisResult(result.result);
        toast.current?.show({
          severity: 'success',
          summary: '圖片分析完成',
          detail: '圖片分析結果已生成！',
          life: 3000
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `圖片${isAnalysisProvider ? '分析' : '生成'}失敗`;
      setError(errorMessage);

      // 根據錯誤類型顯示不同的 Toast 訊息
      const isQuotaError = errorMessage.includes('配額') || errorMessage.includes('RESOURCE_EXHAUSTED');
      const isPermissionError = errorMessage.includes('權限') || errorMessage.includes('PERMISSION_DENIED');

      toast.current?.show({
        severity: isQuotaError ? 'warn' : isPermissionError ? 'error' : 'error',
        summary: isQuotaError ? 'API 配額限制' : isPermissionError ? 'API Key 問題' : `${isAnalysisProvider ? '分析' : '生成'}失敗`,
        detail: errorMessage,
        life: isQuotaError ? 8000 : 5000
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadResult = () => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    if (generatedImage) {
      downloadResult('generation', generatedImage, `ai-generated-${timestamp}.png`);
      toast.current?.show({
        severity: 'success',
        summary: '下載開始',
        detail: '圖片下載已開始',
        life: 2000
      });
    } else if (analysisResult) {
      downloadResult('analysis', analysisResult, `ai-analysis-${timestamp}.txt`);
      toast.current?.show({
        severity: 'success',
        summary: '下載開始',
        detail: '分析結果下載已開始',
        life: 3000
      });
    }
  };

  const handleClearAll = () => {
    setPrompt('');
    setRefinementPrompt('');
    setReferenceImage(null);
    setReferenceImagePreview(null);
    setGeneratedImage(null);
    setAnalysisResult(null);
    setError(null);
  };

  return (
    <>
      <Toast ref={toast} />
      <Card
        title={isAnalysisProvider ? "圖片分析" : "圖片生成"}
        className={`w-full ${className}`}
        pt={{
          root: { className: 'shadow-lg border-0' },
          title: { className: 'text-xl font-bold text-green-600 mb-4' },
          content: { className: 'pt-0' }
        }}
      >
        <div className="flex flex-col gap-6">
          {/* 提示詞設定區域 */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">提示詞設定</h3>

            <div className="flex flex-col gap-4">
              {/* 預設提示詞選擇 */}
              <div className="flex flex-col gap-2">
                <label htmlFor="prompt-selector" className="font-medium text-gray-700">
                  選擇預設提示詞
                </label>
                <Dropdown
                  id="prompt-selector"
                  value={null}
                  options={promptOptions}
                  onChange={handlePromptSelection}
                  placeholder="選擇預設提示詞或使用自訂"
                  className="w-full"
                  optionLabel="label"
                  itemTemplate={(option) => (
                    <div className="flex items-center gap-2 p-2">
                      <i className={`pi ${option.icon} text-gray-600`}></i>
                      <div className="flex-1">
                        <div className="font-medium">{option.label}</div>
                        {option.description && (
                          <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                        )}
                      </div>
                    </div>
                  )}
                  pt={{
                    root: { className: 'border border-gray-300 rounded-lg' },
                    input: { className: 'p-3' },
                    trigger: { className: 'p-3' },
                    panel: { className: 'border border-gray-300 rounded-lg shadow-lg' },
                    item: { className: 'hover:bg-gray-50' }
                  }}
                />
                <div className="text-xs text-gray-500">
                  選擇預設提示詞會自動填入基礎提示詞欄位
                </div>
              </div>

              {/* 基礎提示詞輸入 */}
              <div className="flex flex-col gap-2">
                <label htmlFor="prompt" className="font-medium text-gray-700">
                  基礎提示詞 <span className="text-red-500">*</span>
                </label>
                <InputTextarea
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder={
                    isAnalysisProvider
                      ? "請描述您想要了解圖片的哪些方面，例如：請詳細描述這張圖片的內容和風格"
                      : "請描述您想要生成的圖片，例如：一個可愛的公仔，背景是城市夜景"
                  }
                  rows={3}
                  className="w-full"
                  pt={{
                    root: { className: 'border border-gray-300 rounded-lg focus-within:border-green-500' }
                  }}
                />
              </div>

              {/* 微調提示詞輸入 */}
              <div className="flex flex-col gap-2">
                <label htmlFor="refinement-prompt" className="font-medium text-gray-700">
                  微調提示詞 <span className="text-gray-400">(選填)</span>
                </label>
                <InputTextarea
                  id="refinement-prompt"
                  value={refinementPrompt}
                  onChange={(e) => setRefinementPrompt(e.target.value)}
                  placeholder="請輸入額外的細節描述，例如：穿著牛仔褲，戴著黃色的帽子"
                  rows={2}
                  className="w-full"
                  pt={{
                    root: { className: 'border border-gray-300 rounded-lg focus-within:border-green-500' }
                  }}
                />
                <div className="text-xs text-gray-500">
                  微調提示詞將與基礎提示詞自動組合，生成更精確的描述
                </div>
              </div>
            </div>
          </div>

          {/* 參考圖片上傳 */}
          <ImageUpload />

          {/* 錯誤訊息 */}
          <ErrorDisplay
            error={error}
            onRetry={handleProcessImage}
            onDismiss={() => setError(null)}
          />

          {/* 操作按鈕 */}
          <div className="flex gap-3 flex-wrap">
            <Button
              label={isGenerating ? (isAnalysisProvider ? '分析中...' : '生成中...') : (isAnalysisProvider ? '分析圖片' : '生成圖片')}
              onClick={handleProcessImage}
              loading={isGenerating}
              disabled={!isApiKeyValid || !prompt.trim() || !isImageModel || isGenerating || (isAnalysisProvider && !referenceImage)}
              className="flex-1 min-w-[120px] bg-green-500 hover:bg-green-600 text-white font-medium py-3 border-0 rounded-lg"
              icon={isGenerating ? undefined : (isAnalysisProvider ? 'pi pi-search' : 'pi pi-image')}
            />
            <Button
              label="清除全部"
              onClick={handleClearAll}
              disabled={isGenerating}
              className="px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white font-medium border-0 rounded-lg"
              icon="pi pi-trash"
            />
          </div>

          {/* 處理中的載入動畫 */}
          {isGenerating && (
            <div className="flex flex-col items-center gap-4 py-8">
              <ProgressSpinner style={{ width: '50px', height: '50px' }} />
              <p className="text-gray-600">
                正在{isAnalysisProvider ? '分析圖片' : '生成圖片'}，請稍候...
              </p>
            </div>
          )}

          {/* 圖片生成結果 */}
          {generatedImage && !isGenerating && (
            <div className="flex flex-col gap-4">
              <h3 className="text-lg font-bold text-gray-800">生成結果</h3>
              <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                <div className="flex justify-center image-preview-container">
                  <Image
                    src={generatedImage}
                    alt="生成的圖片"
                    className="max-w-full max-h-96 rounded-lg object-contain"
                    preview
                    pt={{
                      root: { className: 'max-w-full image-preview-container' },
                      image: { className: 'max-w-full max-h-96 object-contain' }
                    }}
                  />
                </div>
                <div className="flex justify-center mt-4">
                  <Button
                    label="下載圖片"
                    onClick={handleDownloadResult}
                    icon="pi pi-download"
                    className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 border-0 rounded-lg"
                  />
                </div>
              </div>
            </div>
          )}

          {/* 圖片分析結果 */}
          {analysisResult && !isGenerating && (
            <div className="flex flex-col gap-4">
              <h3 className="text-lg font-bold text-gray-800">分析結果</h3>
              <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                  {analysisResult}
                </div>
                <div className="flex justify-center mt-4">
                  <Button
                    label="下載分析結果"
                    onClick={handleDownloadResult}
                    icon="pi pi-download"
                    className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 border-0 rounded-lg"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
    </>
  );
}
