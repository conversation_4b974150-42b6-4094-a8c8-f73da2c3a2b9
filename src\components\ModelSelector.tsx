'use client';

import React, { useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';
import { useModel } from '@/hooks/useGemini';
import {
  GEMINI_MODELS,
  OPENROUTER_MODELS,
  ALL_MODELS,
  AIModel,
  AIProvider
} from '@/hooks/useGemini';

interface ModelSelectorProps {
  className?: string;
}

export default function ModelSelector({ className = '' }: ModelSelectorProps) {
  const { selectedModel, setSelectedModel, selectedProvider } = useModel();

  // 根據選擇的提供商過濾模型
  const getAvailableModels = (provider: AIProvider) => {
    switch (provider) {
      case 'gemini':
        return GEMINI_MODELS;
      case 'openrouter':
        return OPENROUTER_MODELS;
      default:
        return [];
    }
  };

  const availableModels = getAvailableModels(selectedProvider);

  const modelOptions = availableModels.map(model => ({
    label: model.label,
    value: model.value,
    type: model.type,
    provider: model.provider,
  }));

  // 當提供商切換時，自動選擇該提供商的第一個模型
  useEffect(() => {
    if (availableModels.length > 0) {
      const currentModelProvider = ALL_MODELS.find(m => m.value === selectedModel)?.provider;
      if (currentModelProvider !== selectedProvider) {
        setSelectedModel(availableModels[0].value);
      }
    }
  }, [selectedProvider, availableModels, selectedModel, setSelectedModel]);

  const handleModelChange = (e: { value: AIModel }) => {
    setSelectedModel(e.value);
  };

  const selectedModelInfo = ALL_MODELS.find(model => model.value === selectedModel);

  // 自定義選項模板
  const optionTemplate = (option: typeof modelOptions[0]) => {
    return (
      <div className="flex items-center justify-between w-full">
        <span>{option.label}</span>
        <Badge
          value={option.type === 'image' ? '圖片' : '文字'}
          severity={option.type === 'image' ? 'success' : 'info'}
          className="ml-2"
        />
      </div>
    );
  };

  // 自定義選中值模板
  const valueTemplate = (option: typeof modelOptions[0]) => {
    if (option) {
      return (
        <div className="flex items-center justify-between w-full">
          <span>{option.label}</span>
          <Badge
            value={option.type === 'image' ? '圖片' : '文字'}
            severity={option.type === 'image' ? 'success' : 'info'}
            className="ml-2"
          />
        </div>
      );
    }
    return <span>請選擇模型</span>;
  };

  // 獲取提供商名稱
  const getProviderName = (provider: AIProvider) => {
    switch (provider) {
      case 'gemini':
        return 'Gemini';
      case 'openrouter':
        return 'OpenRouter';
      default:
        return 'AI';
    }
  };

  return (
    <Card
      title="模型選擇"
      className={`w-full ${className}`}
      pt={{
        root: { className: 'shadow-lg border-0' },
        title: { className: 'text-xl font-bold text-purple-600 mb-4' },
        content: { className: 'pt-0' }
      }}
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <label htmlFor="model-selector" className="font-medium text-gray-700">
            選擇 {getProviderName(selectedProvider)} 模型
          </label>
          <Dropdown
            id="model-selector"
            value={selectedModel}
            options={modelOptions}
            onChange={handleModelChange}
            optionLabel="label"
            optionValue="value"
            placeholder="請選擇模型"
            className="w-full"
            itemTemplate={optionTemplate}
            valueTemplate={valueTemplate}
            pt={{
              root: { className: 'border border-gray-300 rounded-lg' },
              input: { className: 'p-3' },
              trigger: { className: 'p-3' },
              panel: { className: 'border border-gray-300 rounded-lg shadow-lg' },
              item: { className: 'p-3 hover:bg-gray-50' }
            }}
          />
        </div>

        {selectedModelInfo && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg border border-purple-200">
            <div className="flex items-center gap-2 mb-2">
              <i className="pi pi-info-circle text-purple-600"></i>
              <span className="font-medium text-purple-800">模型資訊</span>
            </div>
            <div className="text-sm text-gray-700 space-y-1">
              <p><strong>名稱：</strong>{selectedModelInfo.label}</p>
              <p><strong>類型：</strong>
                <Badge
                  value={selectedModelInfo.type === 'image' ? '圖片生成模型' : '文字模型'}
                  severity={selectedModelInfo.type === 'image' ? 'success' : 'info'}
                  className="ml-2"
                />
              </p>
              <p><strong>用途：</strong>
                {selectedModelInfo.type === 'image'
                  ? '用於生成圖片內容'
                  : '用於文字處理和提示詞組合'
                }
              </p>
            </div>
          </div>
        )}

        <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-start gap-2">
            <i className="pi pi-lightbulb mt-0.5 text-yellow-500"></i>
            <div>
              <p className="font-medium mb-1">模型說明：</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                {selectedProvider === 'gemini' && (
                  <>
                    <li><strong>Gemini 2.0 Flash Preview Image Generation：</strong>最新的圖片生成模型，預設選擇，具備優秀的生成品質和速度</li>
                    <li><strong>Gemini 2.5 Flash Image Preview：</strong>穩定的圖片生成模型，速度快且效果佳</li>
                    <li><strong>Imagen 3.0 Generate：</strong>高品質圖片生成模型，適合精細圖片創作</li>
                    <li><strong>Gemini 2.0 Flash：</strong>文字模型，用於提示詞組合和優化</li>
                  </>
                )}
                {selectedProvider === 'openrouter' && (
                  <>
                    <li><strong>Gemini 2.5 Flash Image Preview (Free)：</strong>免費的圖片分析模型，可以分析圖片內容並提供詳細描述</li>
                    <li>此模型專門用於圖片分析，不支援圖片生成功能</li>
                    <li>需要上傳圖片才能進行分析處理</li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
