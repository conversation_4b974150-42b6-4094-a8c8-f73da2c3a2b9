export interface PromptItem {
  id: string;
  name: string;
  description: string;
  prompt: string;
  icon: string;
}



// 預設提示詞資料
export const DEFAULT_PROMPTS: PromptItem[] = [
  {
    id: 'computer-desk-figurine',
    name: '電腦桌公仔',
    description: '放置在電腦桌上的精緻公仔模型',
    prompt: "create a 1/7 scale commercialized figurine of the characters in the picture, in a realistic style, in a real environment. The figurine is placed on a computer desk. The figurine has a round transparent acrylic base, with no text on the base. The content on the computer screen is the Zbrush modelling process of this figurine. Illustrations. Please turn this photo into a figure. Behind it, there should be a Model packaging box with the character from this photo printed on it. In front of the box, on a round plastic base, place the figure version of the photo I gave you. I'd like the PVC material to be clearly represented.It would be even better if the background is indoors.",
    icon: 'pi-user'
  },
  {
    id: 'ghibli-style',
    name: '吉卜力風格',
    description: '將圖片轉換為吉卜力動畫風格',
    prompt: '請將照片生成吉卜力風格。',
    icon: 'pi-palette'
  }
];

// 根據名稱或描述搜尋提示詞
export function searchPrompts(query: string): PromptItem[] {
  const lowerQuery = query.toLowerCase();
  return DEFAULT_PROMPTS.filter(prompt =>
    prompt.name.toLowerCase().includes(lowerQuery) ||
    prompt.description.toLowerCase().includes(lowerQuery)
  );
}
