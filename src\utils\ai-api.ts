// 統一的 AI API 模組
// 整合 Gemini 和 OpenRouter API

import { 
  validateApi<PERSON>ey as validateGeminiApiKey,
  generateImage as generateGeminiImage,
  combinePrompts as combineGeminiPrompts,
  downloadImage as downloadGeminiImage,
  GeminiAPIError
} from './gemini-api';

import {
  validateOpenRouterApiKey,
  analyzeImageWithOpenRouter,
  downloadAnalysisResult,
  OpenRouterAPIError
} from './openrouter-api';

import { AIProvider, AIModel } from '@/hooks/useGemini';

// 統一的 API 錯誤類型
export class AIAPIError extends Error {
  constructor(
    message: string,
    public provider: AIProvider,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AIAPIError';
  }

  getLocalizedMessage(): string {
    if (this.originalError instanceof GeminiAPIError) {
      return this.originalError.getLocalizedMessage();
    }
    if (this.originalError instanceof OpenRouterAPIError) {
      return this.originalError.getLocalizedMessage();
    }
    return this.message;
  }
}

/**
 * 驗證指定提供商的 API Key
 */
export async function validateProviderApiKey(
  provider: AIProvider,
  apiKey: string
): Promise<{ isValid: boolean; error?: string }> {
  try {
    switch (provider) {
      case 'gemini':
        return await validateGeminiApiKey(apiKey);
      case 'openrouter':
        return await validateOpenRouterApiKey(apiKey);
      default:
        return {
          isValid: false,
          error: `不支援的提供商: ${provider}`
        };
    }
  } catch (error) {
    console.error(`${provider} API Key 驗證失敗:`, error);
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'API Key 驗證失敗'
    };
  }
}

/**
 * 根據提供商和模型處理圖片
 */
export async function processImage(
  provider: AIProvider,
  apiKey: string,
  prompt: string,
  model: AIModel,
  referenceImage?: File
): Promise<{ type: 'generation' | 'analysis'; result: string }> {
  try {
    switch (provider) {
      case 'gemini':
        if (!referenceImage) {
          // 純文字生成圖片
          const imageDataUrl = await generateGeminiImage(apiKey, prompt, model);
          return { type: 'generation', result: imageDataUrl };
        } else {
          // 基於參考圖片生成新圖片
          const imageDataUrl = await generateGeminiImage(apiKey, prompt, model, referenceImage);
          return { type: 'generation', result: imageDataUrl };
        }

      case 'openrouter':
        if (!referenceImage) {
          throw new AIAPIError(
            'OpenRouter 需要參考圖片進行分析',
            provider
          );
        }
        // 分析圖片內容
        const analysisResult = await analyzeImageWithOpenRouter(apiKey, prompt, model, referenceImage);
        return { type: 'analysis', result: analysisResult };

      default:
        throw new AIAPIError(
          `不支援的提供商: ${provider}`,
          provider
        );
    }
  } catch (error) {
    console.error(`${provider} 圖片處理失敗:`, error);
    
    if (error instanceof AIAPIError) {
      throw error;
    }
    
    throw new AIAPIError(
      error instanceof Error ? error.message : '圖片處理失敗',
      provider,
      error instanceof Error ? error : undefined
    );
  }
}

/**
 * 組合提示詞（僅適用於 Gemini）
 */
export async function combinePrompts(
  provider: AIProvider,
  apiKey: string,
  basePrompt: string,
  refinementPrompt: string
): Promise<string> {
  try {
    switch (provider) {
      case 'gemini':
        return await combineGeminiPrompts(apiKey, basePrompt, refinementPrompt);
      case 'openrouter':
        // OpenRouter 不支援提示詞組合，直接返回組合後的文字
        return `${basePrompt}\n\n${refinementPrompt}`;
      default:
        throw new AIAPIError(
          `不支援的提供商: ${provider}`,
          provider
        );
    }
  } catch (error) {
    console.error(`${provider} 提示詞組合失敗:`, error);
    
    if (error instanceof AIAPIError) {
      throw error;
    }
    
    throw new AIAPIError(
      error instanceof Error ? error.message : '提示詞組合失敗',
      provider,
      error instanceof Error ? error : undefined
    );
  }
}

/**
 * 下載結果
 */
export function downloadResult(
  type: 'generation' | 'analysis',
  result: string,
  filename?: string
): void {
  try {
    switch (type) {
      case 'generation':
        // 下載生成的圖片
        downloadGeminiImage(result, filename);
        break;
      case 'analysis':
        // 下載分析結果
        downloadAnalysisResult(result, filename);
        break;
      default:
        throw new Error(`不支援的結果類型: ${type}`);
    }
  } catch (error) {
    console.error('下載結果失敗:', error);
    throw new Error(error instanceof Error ? error.message : '下載結果失敗');
  }
}

/**
 * 獲取提供商的功能描述
 */
export function getProviderCapabilities(provider: AIProvider): {
  name: string;
  description: string;
  capabilities: string[];
  requiresImage: boolean;
} {
  switch (provider) {
    case 'gemini':
      return {
        name: 'Google Gemini',
        description: '使用 Google Gemini API 進行圖片生成',
        capabilities: [
          '文字轉圖片生成',
          '基於參考圖片的圖片生成',
          '提示詞智能組合',
          '多種圖片生成模型'
        ],
        requiresImage: false
      };
    case 'openrouter':
      return {
        name: 'OpenRouter',
        description: '使用 OpenRouter API 進行圖片分析',
        capabilities: [
          '圖片內容分析',
          '圖片描述生成',
          '免費模型支援',
          '多語言分析結果'
        ],
        requiresImage: true
      };
    default:
      return {
        name: '未知提供商',
        description: '不支援的提供商',
        capabilities: [],
        requiresImage: false
      };
  }
}
