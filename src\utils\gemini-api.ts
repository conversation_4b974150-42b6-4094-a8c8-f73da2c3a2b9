// Gemini API 相關的類型定義
export interface GeminiTextPart {
  text: string;
}

export interface GeminiInlineDataPart {
  inlineData: {
    mimeType: string;
    data: string;
  };
}

export type GeminiPart = GeminiTextPart | GeminiInlineDataPart;

export interface GeminiContent {
  parts: GeminiPart[];
}

export interface GeminiRequest {
  contents: GeminiContent[];
  generationConfig?: {
    responseMimeType?: string;
    responseModalities?: string[];
  };
}

export interface GeminiResponse {
  candidates: Array<{
    content?: {
      parts: Array<{
        text?: string;
        inlineData?: {
          mimeType: string;
          data: string;
        };
      }>;
    };
    finishReason?: string;
    index?: number;
    safetyRatings?: Array<{
      probability?: string;
      blocked?: boolean;
    }>;
  }>;
  usageMetadata?: {
    promptTokenCount?: number;
    totalTokenCount?: number;
    promptTokensDetails?: Array<{
      modality?: string;
      tokenCount?: number;
    }>;
  };
  modelVersion?: string;
  responseId?: string;
}

export interface GeminiErrorResponse {
  error: {
    code: number;
    message: string;
    status: string;
    details?: Array<{
      '@type': string;
      reason?: string;
      domain?: string;
      metadata?: Record<string, unknown>;
    }>;
  };
}

export class GeminiAPIError extends Error {
  public code: number;
  public status: string;
  public details?: Array<{
    '@type': string;
    reason?: string;
    domain?: string;
    metadata?: Record<string, unknown>;
  }>;

  constructor(errorResponse: GeminiErrorResponse['error']) {
    super(errorResponse.message);
    this.name = 'GeminiAPIError';
    this.code = errorResponse.code;
    this.status = errorResponse.status;
    this.details = errorResponse.details;
  }

  public getLocalizedMessage(): string {
    switch (this.code) {
      case 400:
        if (this.status === 'INVALID_ARGUMENT') {
          return '請求參數無效，請檢查提示詞或參考圖片格式';
        }
        return '請求格式錯誤，請稍後再試';

      case 401:
        return 'API Key 無效或已過期，請重新設定 API Key';

      case 403:
        if (this.status === 'PERMISSION_DENIED') {
          return 'API Key 沒有使用此功能的權限，請檢查 API Key 設定';
        }
        return '存取被拒絕，請檢查 API Key 權限';

      case 404:
        return '找不到指定的模型，請選擇其他可用模型';

      case 429:
        if (this.status === 'RESOURCE_EXHAUSTED') {
          return 'API 配額已用完，請稍後再試或檢查您的配額限制';
        }
        return '請求過於頻繁，請稍後再試';

      case 500:
        return '伺服器內部錯誤，請稍後再試';

      case 503:
        return '服務暫時無法使用，請稍後再試';

      default:
        return this.message || '發生未知錯誤，請稍後再試';
    }
  }
}

// API 基礎 URL
const GEMINI_API_BASE_URL = 'https://generativelanguage.googleapis.com';

/**
 * 處理 API 回應並檢查錯誤
 */
async function handleApiResponse(response: Response): Promise<GeminiResponse> {
  const responseText = await response.text();

  try {
    const data = JSON.parse(responseText);

    if (!response.ok) {
      // 檢查是否為 Gemini API 錯誤格式
      if (data.error) {
        throw new GeminiAPIError(data.error);
      }

      // 一般 HTTP 錯誤
      throw new Error(`API 請求失敗: ${response.status} ${response.statusText}`);
    }

    return data;
  } catch (parseError) {
    if (parseError instanceof GeminiAPIError) {
      throw parseError;
    }

    // JSON 解析失敗
    if (!response.ok) {
      throw new Error(`API 請求失敗: ${response.status} ${response.statusText}`);
    }

    throw new Error('API 回應格式錯誤');
  }
}

/**
 * 驗證 API Key 是否有效
 */
export async function validateApiKey(apiKey: string): Promise<{ isValid: boolean; error?: string }> {
  try {
    const response = await fetch(
      `${GEMINI_API_BASE_URL}/v1beta/models?key=${apiKey}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.ok) {
      return { isValid: true };
    }

    // 處理錯誤回應
    try {
      await handleApiResponse(response);
      return { isValid: false };
    } catch (apiError) {
      if (apiError instanceof GeminiAPIError) {
        return {
          isValid: false,
          error: apiError.getLocalizedMessage()
        };
      }
      return {
        isValid: false,
        error: apiError instanceof Error ? apiError.message : 'API Key 驗證失敗'
      };
    }
  } catch (error) {
    console.error('API Key 驗證失敗:', error);
    return {
      isValid: false,
      error: '網路連線錯誤，請檢查網路連線後再試'
    };
  }
}

/**
 * 將檔案轉換為 base64 格式
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // 移除 data:image/jpeg;base64, 前綴
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * 呼叫 Gemini 文字模型進行提示詞組合
 */
export async function combinePrompts(
  apiKey: string,
  basePrompt: string,
  refinementPrompt: string,
  model: string = 'gemini-2.0-flash'
): Promise<string> {
  const combinationPrompt = `請將以下兩個提示詞合併為一個用於圖片生成的完整且精確的提示詞。基礎提示詞：「${basePrompt}」。微調提示詞：「${refinementPrompt}」。請直接回傳合併後的提示詞，不要包含其他說明文字。`;

  const request: GeminiRequest = {
    contents: [
      {
        parts: [
          {
            text: combinationPrompt,
          },
        ],
      },
    ],
  };

  try {
    const response = await fetch(
      `${GEMINI_API_BASE_URL}/v1beta/models/${model}:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      }
    );

    const data: GeminiResponse = await handleApiResponse(response);

    if (data.candidates && data.candidates.length > 0) {
      const candidate = data.candidates[0];

      // 檢查是否因為安全性限制而被阻擋
      if (candidate.finishReason === 'SAFETY' || candidate.finishReason === 'IMAGE_SAFETY') {
        throw new Error('提示詞內容被安全性過濾器阻擋，請修改提示詞內容');
      }

      // 檢查是否有其他完成原因
      if (candidate.finishReason && candidate.finishReason !== 'STOP') {
        throw new Error(`API 回應異常結束：${candidate.finishReason}`);
      }

      // 檢查是否有內容
      if (candidate.content && candidate.content.parts) {
        const textPart = candidate.content.parts.find(part => part.text);
        if (textPart && textPart.text) {
          return textPart.text.trim();
        }
      }
    }

    throw new Error('無法從 API 回應中取得合併後的提示詞');
  } catch (error) {
    console.error('提示詞組合失敗:', error);

    if (error instanceof GeminiAPIError) {
      throw new Error(error.getLocalizedMessage());
    }

    throw error;
  }
}

/**
 * 呼叫 Gemini API 生成圖片
 */
export async function generateImage(
  apiKey: string,
  prompt: string,
  model: string,
  referenceImage?: File
): Promise<string> {
  const parts: GeminiPart[] = [
    {
      text: prompt,
    },
  ];

  // 如果有參考圖片，加入到請求中
  if (referenceImage) {
    const base64Data = await fileToBase64(referenceImage);
    parts.push({
      inlineData: {
        mimeType: referenceImage.type,
        data: base64Data,
      },
    });
  }

  // 根據模型類型設定請求格式
  const request: GeminiRequest = {
    contents: [
      {
        parts,
      },
    ],
  };

  // 某些圖片生成模型需要指定回應模式
  if (model === 'gemini-2.0-flash-preview-image-generation') {
    request.generationConfig = {
      responseModalities: ['IMAGE', 'TEXT']
    };
  } else if (model.includes('imagen-3.0')) {
    // Imagen 模型可能也需要特殊配置
    request.generationConfig = {
      responseModalities: ['IMAGE']
    };
  }

  try {
    const response = await fetch(
      `${GEMINI_API_BASE_URL}/v1beta/models/${model}:generateContent?key=${apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      }
    );

    const data: GeminiResponse = await handleApiResponse(response);

    if (data.candidates && data.candidates.length > 0) {
      const candidate = data.candidates[0];

      // 檢查是否因為安全性限制而被阻擋
      if (candidate.finishReason === 'SAFETY' || candidate.finishReason === 'IMAGE_SAFETY') {
        const safetyInfo = candidate.safetyRatings?.some(rating => rating.blocked)
          ? '內容被安全性過濾器阻擋'
          : '內容可能包含不適當的元素';
        throw new Error(`圖片生成失敗：${safetyInfo}，請修改提示詞內容`);
      }

      // 檢查是否有其他完成原因
      if (candidate.finishReason && candidate.finishReason !== 'STOP') {
        throw new Error(`圖片生成異常結束：${candidate.finishReason}`);
      }

      // 檢查是否有內容和圖片資料
      if (candidate.content && candidate.content.parts) {
        const inlineDataPart = candidate.content.parts.find(part => part.inlineData);
        if (inlineDataPart && inlineDataPart.inlineData) {
          return `data:${inlineDataPart.inlineData.mimeType};base64,${inlineDataPart.inlineData.data}`;
        }
      }
    }

    throw new Error('無法從 API 回應中取得生成的圖片');
  } catch (error) {
    console.error('圖片生成失敗:', error);

    if (error instanceof GeminiAPIError) {
      throw new Error(error.getLocalizedMessage());
    }

    throw error;
  }
}

/**
 * 下載圖片
 */
export function downloadImage(imageDataUrl: string, filename: string = 'generated-image.png') {
  const link = document.createElement('a');
  link.href = imageDataUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
