import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 支援的 AI 服務提供商
export const AI_PROVIDERS = [
  {
    value: 'gemini',
    label: 'Google Gemini',
    description: '使用 Google Gemini API 進行圖片生成'
  },
  {
    value: 'openrouter',
    label: 'OpenRouter',
    description: '使用 OpenRouter API 進行圖片分析'
  }
] as const;

export type AIProvider = typeof AI_PROVIDERS[number]['value'];

// 支援的 Gemini 模型
export const GEMINI_MODELS = [
  {
    value: 'gemini-2.0-flash-preview-image-generation',
    label: 'Gemini 2.0 Flash Preview Image Generation',
    type: 'image',
    provider: 'gemini' as const
  },
  {
    value: 'gemini-2.5-flash-image-preview',
    label: 'Gemini 2.5 Flash Image Preview',
    type: 'image',
    provider: 'gemini' as const
  },
  {
    value: 'imagen-3.0-generate-002',
    label: 'Imagen 3.0 Generate',
    type: 'image',
    provider: 'gemini' as const
  },
  {
    value: 'gemini-2.0-flash',
    label: 'Gemini 2.0 Flash',
    type: 'text',
    provider: 'gemini' as const
  }
] as const;

// 支援的 OpenRouter 模型
export const OPENROUTER_MODELS = [
  {
    value: 'google/gemini-2.5-flash-image-preview:free',
    label: 'Gemini 2.5 Flash Image Preview (Free)',
    type: 'image',
    provider: 'openrouter' as const
  }
] as const;

// 所有模型的聯合類型
export const ALL_MODELS = [...GEMINI_MODELS, ...OPENROUTER_MODELS] as const;

export type GeminiModel = typeof GEMINI_MODELS[number]['value'];
export type OpenRouterModel = typeof OPENROUTER_MODELS[number]['value'];
export type AIModel = typeof ALL_MODELS[number]['value'];

// 應用程式狀態介面
interface AIState {
  // 提供商選擇
  selectedProvider: AIProvider;

  // API Key 相關 - 為每個提供商維護獨立的 API Key
  geminiApiKey: string;
  isGeminiApiKeyValid: boolean;
  openrouterApiKey: string;
  isOpenrouterApiKeyValid: boolean;

  // 模型選擇
  selectedModel: AIModel;

  // 圖片生成/分析狀態
  isGenerating: boolean;
  generatedImage: string | null;
  analysisResult: string | null; // 新增：用於 OpenRouter 的圖片分析結果

  // 提示詞
  prompt: string;
  refinementPrompt: string;

  // 參考圖片
  referenceImage: File | null;
  referenceImagePreview: string | null;

  // 錯誤訊息
  error: string | null;

  // Actions
  setSelectedProvider: (provider: AIProvider) => void;
  setGeminiApiKey: (apiKey: string) => void;
  setGeminiApiKeyValid: (isValid: boolean) => void;
  clearGeminiApiKey: () => void;
  setOpenrouterApiKey: (apiKey: string) => void;
  setOpenrouterApiKeyValid: (isValid: boolean) => void;
  clearOpenrouterApiKey: () => void;
  setSelectedModel: (model: AIModel) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  setGeneratedImage: (image: string | null) => void;
  setAnalysisResult: (result: string | null) => void;
  setPrompt: (prompt: string) => void;
  setRefinementPrompt: (prompt: string) => void;
  setReferenceImage: (file: File | null) => void;
  setReferenceImagePreview: (preview: string | null) => void;
  setError: (error: string | null) => void;
  clearAll: () => void;
}

// 建立 Zustand store
export const useAIStore = create<AIState>()(
  persist(
    (set) => ({
      // 初始狀態
      selectedProvider: 'gemini',
      geminiApiKey: '',
      isGeminiApiKeyValid: false,
      openrouterApiKey: '',
      isOpenrouterApiKeyValid: false,
      selectedModel: 'gemini-2.0-flash-preview-image-generation',
      isGenerating: false,
      generatedImage: null,
      analysisResult: null,
      prompt: '',
      refinementPrompt: '',
      referenceImage: null,
      referenceImagePreview: null,
      error: null,

      // Actions
      setSelectedProvider: (provider: AIProvider) => set({ selectedProvider: provider }),
      setGeminiApiKey: (apiKey: string) => set({ geminiApiKey: apiKey }),
      setGeminiApiKeyValid: (isValid: boolean) => set({ isGeminiApiKeyValid: isValid }),
      clearGeminiApiKey: () => set({ geminiApiKey: '', isGeminiApiKeyValid: false }),
      setOpenrouterApiKey: (apiKey: string) => set({ openrouterApiKey: apiKey }),
      setOpenrouterApiKeyValid: (isValid: boolean) => set({ isOpenrouterApiKeyValid: isValid }),
      clearOpenrouterApiKey: () => set({ openrouterApiKey: '', isOpenrouterApiKeyValid: false }),
      setSelectedModel: (model: AIModel) => set({ selectedModel: model }),
      setIsGenerating: (isGenerating: boolean) => set({ isGenerating }),
      setGeneratedImage: (image: string | null) => set({ generatedImage: image }),
      setAnalysisResult: (result: string | null) => set({ analysisResult: result }),
      setPrompt: (prompt: string) => set({ prompt }),
      setRefinementPrompt: (refinementPrompt: string) => set({ refinementPrompt }),
      setReferenceImage: (file: File | null) => set({ referenceImage: file }),
      setReferenceImagePreview: (preview: string | null) => set({ referenceImagePreview: preview }),
      setError: (error: string | null) => set({ error }),
      clearAll: () => set({
        generatedImage: null,
        analysisResult: null,
        prompt: '',
        refinementPrompt: '',
        referenceImage: null,
        referenceImagePreview: null,
        error: null,
        isGenerating: false,
      }),
    }),
    {
      name: 'ai-store',
      // 只持久化部分狀態
      partialize: (state) => ({
        selectedProvider: state.selectedProvider,
        geminiApiKey: state.geminiApiKey,
        isGeminiApiKeyValid: state.isGeminiApiKeyValid,
        openrouterApiKey: state.openrouterApiKey,
        isOpenrouterApiKeyValid: state.isOpenrouterApiKeyValid,
        selectedModel: state.selectedModel,
      }),
    }
  )
);

// 便利的 hooks
export const useProvider = () => {
  const selectedProvider = useAIStore((state) => state.selectedProvider);
  const setSelectedProvider = useAIStore((state) => state.setSelectedProvider);

  return { selectedProvider, setSelectedProvider };
};

export const useApiKey = () => {
  const selectedProvider = useAIStore((state) => state.selectedProvider);
  const geminiApiKey = useAIStore((state) => state.geminiApiKey);
  const isGeminiApiKeyValid = useAIStore((state) => state.isGeminiApiKeyValid);
  const setGeminiApiKey = useAIStore((state) => state.setGeminiApiKey);
  const setGeminiApiKeyValid = useAIStore((state) => state.setGeminiApiKeyValid);
  const clearGeminiApiKey = useAIStore((state) => state.clearGeminiApiKey);
  const openrouterApiKey = useAIStore((state) => state.openrouterApiKey);
  const isOpenrouterApiKeyValid = useAIStore((state) => state.isOpenrouterApiKeyValid);
  const setOpenrouterApiKey = useAIStore((state) => state.setOpenrouterApiKey);
  const setOpenrouterApiKeyValid = useAIStore((state) => state.setOpenrouterApiKeyValid);
  const clearOpenrouterApiKey = useAIStore((state) => state.clearOpenrouterApiKey);

  // 根據當前選擇的提供商返回對應的 API Key 資訊
  const apiKey = selectedProvider === 'gemini' ? geminiApiKey : openrouterApiKey;
  const isApiKeyValid = selectedProvider === 'gemini' ? isGeminiApiKeyValid : isOpenrouterApiKeyValid;
  const setApiKey = selectedProvider === 'gemini' ? setGeminiApiKey : setOpenrouterApiKey;
  const setApiKeyValid = selectedProvider === 'gemini' ? setGeminiApiKeyValid : setOpenrouterApiKeyValid;
  const clearApiKey = selectedProvider === 'gemini' ? clearGeminiApiKey : clearOpenrouterApiKey;

  return {
    selectedProvider,
    apiKey,
    isApiKeyValid,
    setApiKey,
    setApiKeyValid,
    clearApiKey,
    // 也提供直接存取各提供商 API Key 的方法
    geminiApiKey,
    isGeminiApiKeyValid,
    setGeminiApiKey,
    setGeminiApiKeyValid,
    clearGeminiApiKey,
    openrouterApiKey,
    isOpenrouterApiKeyValid,
    setOpenrouterApiKey,
    setOpenrouterApiKeyValid,
    clearOpenrouterApiKey
  };
};

export const useModel = () => {
  const selectedModel = useAIStore((state) => state.selectedModel);
  const setSelectedModel = useAIStore((state) => state.setSelectedModel);
  const selectedProvider = useAIStore((state) => state.selectedProvider);

  return { selectedModel, setSelectedModel, selectedProvider };
};

export const useImageGeneration = () => {
  const isGenerating = useAIStore((state) => state.isGenerating);
  const generatedImage = useAIStore((state) => state.generatedImage);
  const analysisResult = useAIStore((state) => state.analysisResult);
  const setIsGenerating = useAIStore((state) => state.setIsGenerating);
  const setGeneratedImage = useAIStore((state) => state.setGeneratedImage);
  const setAnalysisResult = useAIStore((state) => state.setAnalysisResult);

  return {
    isGenerating,
    generatedImage,
    analysisResult,
    setIsGenerating,
    setGeneratedImage,
    setAnalysisResult
  };
};

export const usePrompts = () => {
  const prompt = useAIStore((state) => state.prompt);
  const refinementPrompt = useAIStore((state) => state.refinementPrompt);
  const setPrompt = useAIStore((state) => state.setPrompt);
  const setRefinementPrompt = useAIStore((state) => state.setRefinementPrompt);

  return { prompt, refinementPrompt, setPrompt, setRefinementPrompt };
};

export const useReferenceImage = () => {
  const referenceImage = useAIStore((state) => state.referenceImage);
  const referenceImagePreview = useAIStore((state) => state.referenceImagePreview);
  const setReferenceImage = useAIStore((state) => state.setReferenceImage);
  const setReferenceImagePreview = useAIStore((state) => state.setReferenceImagePreview);

  return { referenceImage, referenceImagePreview, setReferenceImage, setReferenceImagePreview };
};

export const useError = () => {
  const error = useAIStore((state) => state.error);
  const setError = useAIStore((state) => state.setError);

  return { error, setError };
};


