async function encodeImageToBase64(imagePath: string): Promise<string> {
    const imageBuffer = await fs.promises.readFile(imagePath);
    const base64Image = imageBuffer.toString('base64');
    return `data:image/jpeg;base64,${base64Image}`;
}

// Read and encode the image
const imagePath = 'path/to/your/image.jpg';
const base64Image = await encodeImageToBase64(imagePath);

const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
        Authorization: `Bearer ${API_KEY_REF}`,
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        model: '{{MODEL}}',
        messages: [
            {
                role: 'user',
                content: [
                    {
                        type: 'text',
                        text: "What's in this image?",
                    },
                    {
                        type: 'image_url',
                        image_url: {
                            url: base64Image,
                        },
                    },
                ],
            },
        ],
    }),
});

const data = await response.json();
console.log(data);
