'use client';

import React from 'react';
import { Card } from 'primereact/card';
import { RadioButton } from 'primereact/radiobutton';
import { Badge } from 'primereact/badge';
import { useProvider } from '@/hooks/useGemini';
import { AI_PROVIDERS, AIProvider } from '@/hooks/useGemini';
import { getProviderCapabilities } from '@/utils/ai-api';

interface ProviderSelectorProps {
  className?: string;
}

export default function ProviderSelector({ className = '' }: ProviderSelectorProps) {
  const { selectedProvider, setSelectedProvider } = useProvider();

  const handleProviderChange = (provider: AIProvider) => {
    setSelectedProvider(provider);
  };

  return (
    <Card
      title="AI 服務提供商"
      className={`w-full ${className}`}
      pt={{
        root: { className: 'shadow-lg border-0' },
        title: { className: 'text-xl font-bold text-indigo-600 mb-4' },
        content: { className: 'pt-0' }
      }}
    >
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-3">
          <label className="font-medium text-gray-700">
            選擇 AI 服務提供商
          </label>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {AI_PROVIDERS.map((provider) => {
              const capabilities = getProviderCapabilities(provider.value);
              const isSelected = selectedProvider === provider.value;
              
              return (
                <div
                  key={provider.value}
                  className={`
                    relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                    ${isSelected 
                      ? 'border-indigo-500 bg-indigo-50 shadow-md' 
                      : 'border-gray-200 bg-white hover:border-indigo-300 hover:bg-indigo-25'
                    }
                  `}
                  onClick={() => handleProviderChange(provider.value)}
                >
                  <div className="flex items-start gap-3">
                    <RadioButton
                      inputId={provider.value}
                      name="provider"
                      value={provider.value}
                      onChange={(e) => handleProviderChange(e.value)}
                      checked={selectedProvider === provider.value}
                      className="mt-1"
                    />
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <label 
                          htmlFor={provider.value} 
                          className="font-semibold text-gray-800 cursor-pointer"
                        >
                          {provider.label}
                        </label>
                        {capabilities.requiresImage && (
                          <Badge 
                            value="需要圖片" 
                            severity="warning" 
                            className="text-xs"
                          />
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">
                        {provider.description}
                      </p>
                      
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-gray-700">主要功能：</p>
                        <ul className="text-xs text-gray-600 space-y-0.5">
                          {capabilities.capabilities.slice(0, 3).map((capability, index) => (
                            <li key={index} className="flex items-center gap-1">
                              <i className="pi pi-check text-green-500 text-xs"></i>
                              {capability}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <i className="pi pi-check-circle text-indigo-500 text-lg"></i>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* 顯示選中提供商的詳細資訊 */}
        {selectedProvider && (
          <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-4 rounded-lg border border-indigo-200">
            <div className="flex items-center gap-2 mb-2">
              <i className="pi pi-info-circle text-indigo-600"></i>
              <span className="font-medium text-indigo-800">
                {getProviderCapabilities(selectedProvider).name} 功能說明
              </span>
            </div>
            
            <div className="text-sm text-gray-700 space-y-2">
              <p>
                <strong>描述：</strong>
                {getProviderCapabilities(selectedProvider).description}
              </p>
              
              <div>
                <strong>完整功能列表：</strong>
                <ul className="list-disc list-inside mt-1 space-y-0.5 ml-4">
                  {getProviderCapabilities(selectedProvider).capabilities.map((capability, index) => (
                    <li key={index} className="text-xs">{capability}</li>
                  ))}
                </ul>
              </div>
              
              {getProviderCapabilities(selectedProvider).requiresImage && (
                <div className="bg-yellow-50 border border-yellow-200 rounded p-2 mt-2">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <i className="pi pi-exclamation-triangle text-sm"></i>
                    <span className="text-xs font-medium">
                      注意：此提供商需要上傳圖片才能進行處理
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-start gap-2">
            <i className="pi pi-lightbulb mt-0.5 text-yellow-500"></i>
            <div>
              <p className="font-medium mb-1">使用建議：</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Gemini：</strong>適合需要生成新圖片的創作場景</li>
                <li><strong>OpenRouter：</strong>適合需要分析現有圖片內容的場景</li>
                <li>您可以隨時切換提供商，每個提供商的 API Key 會獨立保存</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
