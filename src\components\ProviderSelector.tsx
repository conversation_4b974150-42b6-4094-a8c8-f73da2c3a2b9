'use client';

import React from 'react';
import { Card } from 'primereact/card';
import { RadioButton } from 'primereact/radiobutton';
import { Badge } from 'primereact/badge';
import { useProvider } from '@/hooks/useGemini';
import { AI_PROVIDERS, AIProvider } from '@/hooks/useGemini';
import { getProviderCapabilities } from '@/utils/ai-api';

interface ProviderSelectorProps {
  className?: string;
}

export default function ProviderSelector({ className = '' }: ProviderSelectorProps) {
  const { selectedProvider, setSelectedProvider } = useProvider();

  const handleProviderChange = (provider: AIProvider) => {
    setSelectedProvider(provider);
  };

  return (
    <Card
      title="AI 服務提供商"
      className={`w-full ${className}`}
      pt={{
        root: { className: 'shadow-lg border-0' },
        title: { className: 'text-xl font-bold text-indigo-600 mb-4' },
        content: { className: 'pt-0' }
      }}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-4">
          <label className="font-medium text-gray-700 text-lg">
            選擇 AI 服務提供商
          </label>

          <div className="grid grid-cols-1 gap-4">
            {AI_PROVIDERS.map((provider) => {
              const capabilities = getProviderCapabilities(provider.value);
              const isSelected = selectedProvider === provider.value;

              return (
                <div
                  key={provider.value}
                  className={`
                    relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200
                    ${isSelected
                      ? 'border-indigo-500 bg-indigo-50 shadow-lg'
                      : 'border-gray-200 bg-white hover:border-indigo-300 hover:bg-indigo-25 hover:shadow-md'
                    }
                  `}
                  onClick={() => handleProviderChange(provider.value)}
                >
                  <div className="flex items-start gap-4">
                    <RadioButton
                      inputId={provider.value}
                      name="provider"
                      value={provider.value}
                      onChange={(e) => handleProviderChange(e.value)}
                      checked={selectedProvider === provider.value}
                      className="mt-1.5"
                    />

                    <div className="flex-1 space-y-3">
                      <div className="flex items-center gap-3 mb-2">
                        <label
                          htmlFor={provider.value}
                          className="font-bold text-gray-800 cursor-pointer text-lg"
                        >
                          {provider.label}
                        </label>
                        {capabilities.requiresImage && (
                          <Badge
                            value="需要圖片"
                            severity="warning"
                            className="text-xs px-2 py-1"
                          />
                        )}
                      </div>

                      <p className="text-sm text-gray-600 leading-relaxed">
                        {provider.description}
                      </p>

                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">主要功能：</p>
                        <ul className="text-sm text-gray-600 space-y-1.5">
                          {capabilities.capabilities.slice(0, 3).map((capability, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <i className="pi pi-check text-green-500 text-sm"></i>
                              <span>{capability}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  {isSelected && (
                    <div className="absolute top-4 right-4">
                      <i className="pi pi-check-circle text-indigo-500 text-xl"></i>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* 顯示選中提供商的詳細資訊 */}
        {selectedProvider && (
          <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-5 rounded-xl border border-indigo-200">
            <div className="flex items-center gap-3 mb-4">
              <i className="pi pi-info-circle text-indigo-600 text-lg"></i>
              <span className="font-semibold text-indigo-800 text-lg">
                {getProviderCapabilities(selectedProvider).name} 功能說明
              </span>
            </div>

            <div className="text-sm text-gray-700 space-y-4">
              <p className="leading-relaxed">
                <strong className="text-gray-800">描述：</strong>
                {getProviderCapabilities(selectedProvider).description}
              </p>

              <div>
                <strong className="text-gray-800">完整功能列表：</strong>
                <ul className="list-disc list-inside mt-2 space-y-1 ml-4">
                  {getProviderCapabilities(selectedProvider).capabilities.map((capability, index) => (
                    <li key={index} className="text-sm leading-relaxed">{capability}</li>
                  ))}
                </ul>
              </div>

              {getProviderCapabilities(selectedProvider).requiresImage && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <i className="pi pi-exclamation-triangle text-sm"></i>
                    <span className="text-sm font-medium">
                      注意：此提供商需要上傳圖片才能進行處理
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="text-sm text-gray-500 bg-gray-50 p-5 rounded-xl">
          <div className="flex items-start gap-3">
            <i className="pi pi-lightbulb mt-1 text-yellow-500 text-lg"></i>
            <div className="space-y-3">
              <p className="font-semibold mb-2 text-gray-700 text-base">使用建議：</p>
              <ul className="list-disc list-inside space-y-2 text-sm leading-relaxed">
                <li><strong className="text-gray-700">Gemini：</strong>適合需要生成新圖片的創作場景</li>
                <li><strong className="text-gray-700">OpenRouter：</strong>適合需要分析現有圖片內容的場景</li>
                <li>您可以隨時切換提供商，每個提供商的 API Key 會獨立保存</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
